
import { Route, Routes, useLocation } from "react-router-dom";
import { Home } from "./pages/home.tsx";
import { Login } from "./pages/login.tsx";
import { Signup } from "./pages/signup.tsx";
import BottomNavbar from './components/ui/BottomNav';
import TopNavbar from './components/ui/topnavbar';
import { AuthProvider, useAuth } from './contexts/AuthContext';

function AppContent() {
  const { user } = useAuth();
  const location = useLocation();

  // Check if current route is login or signup
  const isAuthPage = location.pathname === '/login' || location.pathname === '/signup';

  return (
    <div>
      {/* Show TopNavbar only if user is authenticated or not on auth pages */}
      {(user || !isAuthPage) && <TopNavbar />}

      {/* Show BottomNav only if user is authenticated */}
      {user && <BottomNavbar />}

      <div className={user ? 'pb-16' : ''}>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<Signup />} />
          {/* Add other routes here */}
        </Routes>
      </div>
    </div>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App
