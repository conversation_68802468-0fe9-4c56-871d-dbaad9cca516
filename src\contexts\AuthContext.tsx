import React, { createContext, useContext, useState, useEffect } from 'react'
import type { ReactNode } from 'react'
import type { User, Session, AuthError } from '@supabase/supabase-js'
import { supabase } from '../lib/supabase'

// Types
interface AuthContextType {
  user: User | null
  session: Session | null
  loading: boolean
  signUp: (email: string, password: string, options?: { data?: any }) => Promise<{ user: User | null; error: AuthError | null }>
  signIn: (email: string, password: string) => Promise<{ user: User | null; error: AuthError | null }>
  signInWithOAuth: (provider: 'google' | 'facebook' | 'tiktok') => Promise<{ error: AuthError | null }>
  signOut: () => Promise<{ error: AuthError | null }>
  getCurrentUser: () => Promise<User | null>
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>
}

interface AuthProviderProps {
  children: ReactNode
}

// Create the context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Custom hook to use the auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// AuthProvider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  // Initialize auth state
  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        if (error) {
          console.error('Error getting session:', error.message)
        } else {
          setSession(session)
          setUser(session?.user ?? null)
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error)
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email)

        setSession(session)
        setUser(session?.user ?? null)
        setLoading(false)

        // Handle different auth events
        switch (event) {
          case 'SIGNED_IN':
            console.log('User signed in:', session?.user?.email)
            break
          case 'SIGNED_OUT':
            console.log('User signed out')
            break
          case 'TOKEN_REFRESHED':
            console.log('Token refreshed for user:', session?.user?.email)
            break
          case 'USER_UPDATED':
            console.log('User updated:', session?.user?.email)
            break
          default:
            break
        }
      }
    )

    // Cleanup subscription on unmount
    return () => {
      subscription.unsubscribe()
    }
  }, [])

  // Sign up with email and password
  const signUp = async (email: string, password: string, options?: { data?: any }) => {
    try {
      setLoading(true)
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: options?.data || {}
        }
      })

      if (error) {
        console.error('Sign up error:', error.message)
      } else {
        console.log('Sign up successful:', data.user?.email)
      }

      return { user: data.user, error }
    } catch (error) {
      console.error('Sign up exception:', error)
      return { user: null, error: error as AuthError }
    } finally {
      setLoading(false)
    }
  }

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true)
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        console.error('Sign in error:', error.message)
      } else {
        console.log('Sign in successful:', data.user?.email)
      }

      return { user: data.user, error }
    } catch (error) {
      console.error('Sign in exception:', error)
      return { user: null, error: error as AuthError }
    } finally {
      setLoading(false)
    }
  }

  // Sign in with OAuth providers
  const signInWithOAuth = async (provider: 'google' | 'facebook' | 'tiktok') => {
    try {
      setLoading(true)

      // Map provider names to Supabase provider types
      const providerMap: Record<string, any> = {
        google: 'google',
        facebook: 'facebook',
        tiktok: 'tiktok' // Note: TikTok support depends on Supabase configuration
      }

      const supabaseProvider = providerMap[provider]

      if (!supabaseProvider) {
        throw new Error(`Provider ${provider} is not supported`)
      }

      const { error } = await supabase.auth.signInWithOAuth({
        provider: supabaseProvider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          }
        }
      })

      if (error) {
        console.error(`${provider} OAuth error:`, error.message)
      } else {
        console.log(`${provider} OAuth initiated successfully`)
      }

      return { error }
    } catch (error) {
      console.error(`${provider} OAuth exception:`, error)
      return { error: error as AuthError }
    } finally {
      setLoading(false)
    }
  }

  // Sign out
  const signOut = async () => {
    try {
      setLoading(true)
      const { error } = await supabase.auth.signOut()

      if (error) {
        console.error('Sign out error:', error.message)
      } else {
        console.log('Sign out successful')
        // Clear local state immediately
        setUser(null)
        setSession(null)
      }

      return { error }
    } catch (error) {
      console.error('Sign out exception:', error)
      return { error: error as AuthError }
    } finally {
      setLoading(false)
    }
  }

  // Get current user
  const getCurrentUser = async (): Promise<User | null> => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()

      if (error) {
        console.error('Get current user error:', error.message)
        return null
      }

      return user
    } catch (error) {
      console.error('Get current user exception:', error)
      return null
    }
  }

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`
      })

      if (error) {
        console.error('Reset password error:', error.message)
      } else {
        console.log('Reset password email sent to:', email)
      }

      return { error }
    } catch (error) {
      console.error('Reset password exception:', error)
      return { error: error as AuthError }
    }
  }

  // Context value
  const value: AuthContextType = {
    user,
    session,
    loading,
    signUp,
    signIn,
    signInWithOAuth,
    signOut,
    getCurrentUser,
    resetPassword
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}