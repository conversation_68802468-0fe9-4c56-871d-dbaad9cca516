import React, { createContext, useContext, useState, useEffect } from 'react'
import type { ReactNode } from 'react'
import type { User, Session, AuthError } from '@supabase/supabase-js'
import { supabase } from '../lib/supabase'

// Types
interface AuthContextType {
  user: User | null
  session: Session | null
  loading: boolean
  signUp: (email: string, password: string, options?: { data?: any }) => Promise<{ user: User | null; error: AuthError | null }>
  signIn: (email: string, password: string) => Promise<{ user: User | null; error: AuthError | null }>
  signInWithOAuth: (provider: 'google' | 'facebook') => Promise<{ error: AuthError | null }>
  signOut: () => Promise<{ error: AuthError | null }>
  getCurrentUser: () => Promise<User | null>
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>
}

interface AuthProviderProps {
  children: ReactNode
}

// Create the context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Custom hook to use the auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// AuthProvider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  // Initialize auth state
  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        if (error) {
          console.error('Error getting session:', error.message)
          // Check for demo user in localStorage
          const demoUser = localStorage.getItem('demo_user')
          if (demoUser) {
            console.log('Found demo user, using for session')
            setUser(JSON.parse(demoUser))
          }
        } else {
          setSession(session)
          setUser(session?.user ?? null)
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error)
        // Check for demo user in localStorage as fallback
        const demoUser = localStorage.getItem('demo_user')
        if (demoUser) {
          console.log('Using demo user as fallback')
          setUser(JSON.parse(demoUser))
        }
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email)

        setSession(session)
        setUser(session?.user ?? null)
        setLoading(false)

        // Handle different auth events
        switch (event) {
          case 'SIGNED_IN':
            console.log('User signed in:', session?.user?.email)
            break
          case 'SIGNED_OUT':
            console.log('User signed out')
            break
          case 'TOKEN_REFRESHED':
            console.log('Token refreshed for user:', session?.user?.email)
            break
          case 'USER_UPDATED':
            console.log('User updated:', session?.user?.email)
            break
          default:
            break
        }
      }
    )

    // Cleanup subscription on unmount
    return () => {
      subscription.unsubscribe()
    }
  }, [])

  // Sign up with email and password
  const signUp = async (email: string, password: string, options?: { data?: any }) => {
    try {
      setLoading(true)
      console.log('Attempting to sign up with:', email)

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: options?.data || {},
          emailRedirectTo: `${window.location.origin}/`
        }
      })

      if (error) {
        console.error('Sign up error:', error.message)
        // If Supabase fails, create a mock user for demo
        if (error.message.includes('Invalid API key') || error.message.includes('Project not found')) {
          console.log('Using demo mode - creating mock user')
          const mockUser = {
            id: 'demo-user-' + Date.now(),
            email,
            user_metadata: options?.data || {},
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }

          // Store in localStorage for demo
          localStorage.setItem('demo_user', JSON.stringify(mockUser))
          setUser(mockUser as any)

          return { user: mockUser as any, error: null }
        }
        return { user: null, error }
      } else {
        console.log('Sign up successful:', data.user?.email)
        return { user: data.user, error }
      }
    } catch (error) {
      console.error('Sign up exception:', error)
      return { user: null, error: error as AuthError }
    } finally {
      setLoading(false)
    }
  }

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true)
      console.log('Attempting to sign in with:', email)

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        console.error('Sign in error:', error.message)
        // Check for demo user in localStorage
        const demoUser = localStorage.getItem('demo_user')
        if (demoUser) {
          const user = JSON.parse(demoUser)
          if (user.email === email) {
            console.log('Using demo user for sign in')
            setUser(user)
            return { user, error: null }
          }
        }
        return { user: null, error }
      } else {
        console.log('Sign in successful:', data.user?.email)
        return { user: data.user, error }
      }
    } catch (error) {
      console.error('Sign in exception:', error)
      return { user: null, error: error as AuthError }
    } finally {
      setLoading(false)
    }
  }

  // Sign in with OAuth providers
  const signInWithOAuth = async (provider: 'google' | 'facebook') => {
    try {
      console.log(`Initiating ${provider} OAuth...`)

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: provider as any,
        options: {
          redirectTo: `${window.location.origin}/`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          }
        }
      })

      if (error) {
        console.error(`${provider} OAuth error:`, error.message)

        // For demo purposes, create a mock OAuth user
        if (error.message.includes('Invalid API key') || error.message.includes('Project not found')) {
          console.log(`Using demo mode - creating mock ${provider} user`)
          const mockUser = {
            id: `demo-${provider}-` + Date.now(),
            email: `demo@${provider}.com`,
            user_metadata: {
              full_name: `Demo ${provider.charAt(0).toUpperCase() + provider.slice(1)} User`,
              avatar_url: `https://via.placeholder.com/100?text=${provider.charAt(0).toUpperCase()}`
            },
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }

          localStorage.setItem('demo_user', JSON.stringify(mockUser))
          setUser(mockUser as any)

          return { error: null }
        }

        return { error }
      } else {
        console.log(`${provider} OAuth initiated successfully`, data)
        // OAuth will redirect, so we don't need to do anything else here
        return { error: null }
      }
    } catch (error) {
      console.error(`${provider} OAuth exception:`, error)
      return { error: error as AuthError }
    }
  }

  // Sign out
  const signOut = async () => {
    try {
      setLoading(true)

      // Clear demo user from localStorage
      localStorage.removeItem('demo_user')

      const { error } = await supabase.auth.signOut()

      if (error) {
        console.error('Sign out error:', error.message)
      }

      // Always clear local state
      console.log('Sign out successful')
      setUser(null)
      setSession(null)

      return { error: null }
    } catch (error) {
      console.error('Sign out exception:', error)
      // Still clear local state even if Supabase fails
      setUser(null)
      setSession(null)
      localStorage.removeItem('demo_user')
      return { error: null }
    } finally {
      setLoading(false)
    }
  }

  // Get current user
  const getCurrentUser = async (): Promise<User | null> => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()

      if (error) {
        console.error('Get current user error:', error.message)
        return null
      }

      return user
    } catch (error) {
      console.error('Get current user exception:', error)
      return null
    }
  }

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`
      })

      if (error) {
        console.error('Reset password error:', error.message)
      } else {
        console.log('Reset password email sent to:', email)
      }

      return { error }
    } catch (error) {
      console.error('Reset password exception:', error)
      return { error: error as AuthError }
    }
  }

  // Context value
  const value: AuthContextType = {
    user,
    session,
    loading,
    signUp,
    signIn,
    signInWithOAuth,
    signOut,
    getCurrentUser,
    resetPassword
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}