import { createClient } from '@supabase/supabase-js'

// Check for real Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

// Check if we have valid Supabase configuration
const hasValidConfig = supabaseUrl && supabaseAnonKey && supabaseUrl.includes('supabase.co') && supabaseAnonKey.length > 50

if (!hasValidConfig) {
  console.log('🔧 No Supabase configuration found. Using local demo mode.')
  console.log('📝 To use real Supabase authentication:')
  console.log('1. Create a Supabase project at https://supabase.com')
  console.log('2. Copy your URL and anon key to .env.local')
  console.log('3. Enable Google and Facebook auth in Authentication > Providers')
}

// Create a dummy client for demo mode or real client if configured
const dummyUrl = 'https://localhost.supabase.co'
const dummyKey = 'dummy-key-for-local-development'

export const supabase = hasValidConfig
  ? createClient(supabaseUrl!, supabaseAnonKey!, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        flowType: 'pkce'
      }
    })
  : createClient(dummyUrl, dummyKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false
      }
    })

export { hasValidConfig }
