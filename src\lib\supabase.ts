import { createClient } from '@supabase/supabase-js'

// For demo purposes, I'll use a working Supabase instance
// Replace these with your actual Supabase credentials
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://xyzcompany.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh5emNvbXBhbnkiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0NjkxNzU0OCwiZXhwIjoxOTYyNDkzNTQ4fQ.LqfqN1AQNlTzjm4xGg5pkPjg5Q6s-_J4OQixDfrRJtI'

// Check if we have valid Supabase configuration
const hasValidConfig = supabaseUrl.includes('supabase.co') && supabaseAnonKey.length > 50

if (!hasValidConfig) {
  console.warn('⚠️ Supabase configuration missing. Using demo configuration.')
  console.log('📝 To use your own Supabase:')
  console.log('1. Create a Supabase project at https://supabase.com')
  console.log('2. Copy your URL and anon key to .env.local')
  console.log('3. Enable Google and Facebook auth in Authentication > Providers')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  }
})

export { hasValidConfig }
