// src/components/ui/AvatarMenu.tsx
import { useState, useRef, useEffect } from "react"
import { User, Settings, LogOut, ChevronDown } from "lucide-react"
import { useNavigate } from "react-router-dom"
import { useAuth } from '../../contexts/AuthContext'

interface AvatarMenuProps {
  user: {
    email?: string;
    user_metadata?: {
      avatar_url?: string;
      full_name?: string;
    };
  };
}

export default function AvatarMenu({ user }: AvatarMenuProps) {
  const [open, setOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)
  const navigate = useNavigate()
  const { logout } = useAuth()

  // Close menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleLogout = () => {
    logout()
    setOpen(false)
    navigate('/login')
  }

  const handleProfile = () => {
    navigate('/profile')
    setOpen(false)
  }

  const handleSettings = () => {
    navigate('/settings')
    setOpen(false)
  }

  const displayName = user?.user_metadata?.full_name || user?.email?.split('@')[0] || 'User'
  const avatarUrl = user?.user_metadata?.avatar_url

  return (
    <div className="relative" ref={menuRef}>
      {/* Avatar Button */}
      <button
        onClick={() => setOpen(!open)}
        className="flex items-center space-x-2 p-1.5 md:p-2.5 text-gray-300 hover:text-white bg-white/5 hover:bg-white/10 backdrop-blur-sm border border-white/10 hover:border-[#2de4c2]/30 rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-[#2de4c2]/50 focus:ring-offset-2 focus:ring-offset-gray-900"
        aria-label="User menu"
        aria-expanded={open}
      >
        {/* Avatar Image */}
        <div className="relative">
          {avatarUrl ? (
            <img
              src={avatarUrl}
              alt={displayName}
              className="w-7 h-7 md:w-8 md:h-8 rounded-full object-cover border-2 border-gray-200"
            />
          ) : (
            <div className="w-7 h-7 md:w-8 md:h-8 bg-gradient-to-br from-[#2de4c2] to-[#1fc9a7] rounded-full flex items-center justify-center shadow-lg">
              <User className="w-4 h-4 md:w-5 md:h-5 text-[#18181b]" />
            </div>
          )}
          {/* Online indicator */}
          <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-[#2de4c2] border-2 border-[#18181b] rounded-full shadow-sm"></div>
        </div>

        {/* Name and Chevron (hidden on mobile) */}
        <div className="hidden md:flex items-center space-x-1">
          <span className="text-sm font-medium text-white max-w-24 truncate">
            {displayName}
          </span>
          <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${open ? 'rotate-180' : ''}`} />
        </div>
      </button>

      {/* Dropdown Menu */}
      {open && (
        <div className="absolute right-0 mt-3 w-64 bg-[#1a1a1a] backdrop-blur-xl border border-white/10 rounded-2xl shadow-2xl shadow-black/50 z-50 py-2 animate-in slide-in-from-top-2 duration-200">
          {/* User Info Header */}
          <div className="px-4 py-4 border-b border-white/10">
            <div className="flex items-center space-x-3">
              {avatarUrl ? (
                <img
                  src={avatarUrl}
                  alt={displayName}
                  className="w-12 h-12 rounded-full object-cover ring-2 ring-[#2de4c2]/30"
                />
              ) : (
                <div className="w-12 h-12 bg-gradient-to-br from-[#2de4c2] to-[#1fc9a7] rounded-full flex items-center justify-center shadow-lg">
                  <User className="w-6 h-6 text-[#18181b]" />
                </div>
              )}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-semibold text-white truncate">
                  {displayName}
                </p>
                <p className="text-xs text-gray-400 truncate">
                  {user?.email}
                </p>
                <div className="flex items-center mt-1">
                  <div className="w-2 h-2 bg-[#2de4c2] rounded-full mr-2"></div>
                  <span className="text-xs text-[#2de4c2] font-medium">Online</span>
                </div>
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="py-2">
            <button
              onClick={handleProfile}
              className="flex items-center w-full px-4 py-3 text-sm text-gray-300 hover:bg-white/5 hover:text-white transition-all duration-200 group"
            >
              <User className="w-4 h-4 mr-3 text-gray-400 group-hover:text-[#2de4c2] transition-colors duration-200" />
              <span className="font-medium">Profile</span>
            </button>
            <button
              onClick={handleSettings}
              className="flex items-center w-full px-4 py-3 text-sm text-gray-300 hover:bg-white/5 hover:text-white transition-all duration-200 group"
            >
              <Settings className="w-4 h-4 mr-3 text-gray-400 group-hover:text-[#2de4c2] transition-colors duration-200" />
              <span className="font-medium">Settings</span>
            </button>
          </div>

          {/* Logout Section */}
          <div className="border-t border-white/10 pt-2 pb-1">
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-4 py-3 text-sm text-red-400 hover:bg-red-500/10 hover:text-red-300 transition-all duration-200 group rounded-lg mx-2"
            >
              <LogOut className="w-4 h-4 mr-3 group-hover:scale-110 transition-transform duration-200" />
              <span className="font-medium">Sign out</span>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
