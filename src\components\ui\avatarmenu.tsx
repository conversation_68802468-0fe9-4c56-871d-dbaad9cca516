// src/components/ui/AvatarMenu.jsx
import { useState } from "react"
import { Menu, MenuItem } from "@/components/ui/shadcn-menu"

export default function AvatarMenu({ user }) {
  const [open, setOpen] = useState(false)

  return (
    <div className="relative">
      <img
        src={user?.user_metadata?.avatar_url || "/default.png"}
        className="w-8 h-8 rounded-full cursor-pointer"
        onClick={() => setOpen(!open)}
      />
      {open && (
        <div className="absolute right-0 mt-2 bg-white border rounded shadow w-48 z-50">
          <div className="p-2 text-sm text-gray-600">{user.email}</div>
          <hr />
          <button className="w-full text-left px-4 py-2 hover:bg-gray-100">Profile</button>
          <button className="w-full text-left px-4 py-2 hover:bg-gray-100">Settings</button>
          <button className="w-full text-left px-4 py-2 text-red-500 hover:bg-gray-100">Logout</button>
        </div>
      )}
    </div>
  )
}
