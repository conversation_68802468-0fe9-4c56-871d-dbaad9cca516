// src/components/ui/TopNavbar.tsx
import { Bell, LogIn, UserCircle } from "lucide-react"
import { useNavigate } from "react-router-dom"
import AvatarMenu from "./avatarmenu"

export default function TopNavbar() {
  const user = useUser()
  const navigate = useNavigate()

  return (
    <nav className="w-full bg-white border-b border-gray-200 shadow-sm sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-3 md:px-6 lg:px-8">
        <div className="flex justify-between items-center h-14 md:h-16">
          {/* Logo Section */}
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <h1 className="text-lg md:text-xl lg:text-2xl font-bold text-gray-900 tracking-tight">
                Evoxia
              </h1>
            </div>
          </div>

          {/* Right Section - Navigation Items */}
          <div className="flex items-center space-x-2 md:space-x-4">
            {/* Notifications */}
            <button
              onClick={() => navigate("/notifications")}
              className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              aria-label="Notifications"
            >
              <Bell className="h-5 w-5 md:h-6 md:w-6" />
              <span className="hidden md:inline ml-1 text-sm font-medium">
                Notifications
              </span>
              {/* Notification badge - you can add logic to show unread count */}
              <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
            </button>

            {/* User Section */}
            {user ? (
              <div className="flex items-center">
                <AvatarMenu user={user} />
              </div>
            ) : (
              <div className="flex items-center space-x-1 md:space-x-2">
                <button
                  onClick={() => navigate("/login")}
                  className="flex items-center px-2 md:px-3 py-1.5 md:py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  <LogIn className="h-4 w-4 md:h-5 md:w-5" />
                  <span className="hidden md:inline ml-1">Login</span>
                </button>
                <button
                  onClick={() => navigate("/signup")}
                  className="flex items-center px-2 md:px-4 py-1.5 md:py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  <UserCircle className="h-4 w-4 md:h-5 md:w-5" />
                  <span className="hidden md:inline ml-1">Sign Up</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  )
}
