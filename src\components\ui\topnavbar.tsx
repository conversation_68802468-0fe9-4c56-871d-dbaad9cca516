import { Bell, LogIn, UserCircle } from "lucide-react"
import { Link, useNavigate } from "react-router-dom"
import logo from '/kadzia_logo.png'
import AvatarMenu from './avatarmenu'
import { useAuth } from '../../contexts/AuthContext'

export default function TopNavbar() {
  const navigate = useNavigate()
  const { user } = useAuth()

  // Mock notification count - replace with real data
  const notificationCount = 3

  return (
    <nav className="w-full bg-[#18181b] border-b border-gray-800 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo Section */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-2">
              <img src={logo} alt="kadzia logo" className="h-3 w-3 md:h-4 md:w-4 object-contain" />
            </Link>
          </div>

          {/* Desktop Navigation Links - Hidden on mobile */}
          <div className="hidden md:flex items-center space-x-6">
            <Link
              to="/"
              className="text-gray-300 hover:text-[#2de4c2] px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
            >
              Home
            </Link>
            <Link
              to="/messaging"
              className="text-gray-300 hover:text-[#2de4c2] px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
            >
              Messaging
            </Link>
            <Link
              to="/explore"
              className="text-gray-300 hover:text-[#2de4c2] px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
            >
              Explore
            </Link>
            <Link
              to="/shows"
              className="text-gray-300 hover:text-[#2de4c2] px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
            >
              Shows
            </Link>
          </div>

          {/* Right Section - Navigation Items */}
          <div className="flex items-center space-x-2 md:space-x-4">
            {/* Notifications */}
            <div className="relative group">
              <button
                onClick={() => navigate("/notifications")}
                className="relative flex items-center p-2.5 md:p-3 text-gray-300 hover:text-white bg-white/5 hover:bg-white/10 backdrop-blur-sm border border-white/10 hover:border-[#2de4c2]/30 rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-[#2de4c2]/50 focus:ring-offset-2 focus:ring-offset-gray-900 group-hover:shadow-lg group-hover:shadow-[#2de4c2]/20"
                aria-label={`Notifications ${notificationCount > 0 ? `(${notificationCount} unread)` : ''}`}
              >
                {/* Bell Icon with subtle animation */}
                <div className="relative">
                  <Bell className="h-5 w-5 md:h-6 md:w-6 transition-transform duration-200 group-hover:scale-110" />

                  {/* Notification Badge */}
                  {notificationCount > 0 && (
                    <div className="absolute -top-2 -right-2 flex items-center justify-center">
                      {/* Pulsing background */}
                      <div className="absolute inset-0 bg-[#2de4c2] rounded-full animate-ping opacity-75"></div>
                      {/* Badge content */}
                      <div className="relative bg-gradient-to-r from-[#2de4c2] to-[#1fc9a7] text-[#18181b] text-xs font-bold rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1 shadow-lg">
                        {notificationCount > 99 ? '99+' : notificationCount}
                      </div>
                    </div>
                  )}

                  {/* Subtle glow effect on hover */}
                  <div className="absolute inset-0 bg-[#2de4c2]/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
                </div>

                {/* Text label for larger screens */}
                <span className="hidden lg:inline ml-3 text-sm font-medium transition-colors duration-200">
                  Notifications
                </span>

                {/* Notification count for larger screens */}
                {notificationCount > 0 && (
                  <span className="hidden lg:inline ml-2 px-2 py-0.5 bg-[#2de4c2]/20 text-[#2de4c2] text-xs font-semibold rounded-full border border-[#2de4c2]/30">
                    {notificationCount}
                  </span>
                )}
              </button>

              {/* Tooltip */}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-1.5 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50 lg:hidden">
                {notificationCount > 0 ? `${notificationCount} new notifications` : 'Notifications'}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900"></div>
              </div>
            </div>

            {/* User Section */}
            {user ? (
              <AvatarMenu user={user} />
            ) : (
              <div className="flex items-center space-x-1 md:space-x-2">
                <button
                  onClick={() => navigate("/login")}
                  className="flex items-center px-3 md:px-4 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#2de4c2] focus:ring-offset-2 focus:ring-offset-gray-900"
                >
                  <LogIn className="h-4 w-4 md:h-5 md:w-5" />
                  <span className="hidden md:inline ml-2">Login</span>
                </button>
                <button
                  onClick={() => navigate("/signup")}
                  className="flex items-center px-3 md:px-4 py-2 text-sm font-medium text-black bg-[#2de4c2] hover:bg-[#1fc9a7] rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#2de4c2] focus:ring-offset-2 focus:ring-offset-gray-900"
                >
                  <UserCircle className="h-4 w-4 md:h-5 md:w-5" />
                  <span className="hidden md:inline ml-2">Sign Up</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  )
}
