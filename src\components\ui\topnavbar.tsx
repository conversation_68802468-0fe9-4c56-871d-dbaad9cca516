// src/components/layout/TopNavbar.jsx
import { Bell, LogIn, UserCircle } from "lucide-react"
import { useNavigate } from "react-router-dom"
import { useUser } from "@supabase/auth-helpers-react" // أو useAuth من custom hook
import AvatarMenu from "../ui/AvatarMenu"

export default function TopNavbar() {
  const user = useUser()
  const navigate = useNavigate()

  return (
    <div className="w-full flex justify-between items-center px-4 py-2 bg-white shadow-sm sticky top-0 z-50">
      <h1 className="text-xl font-bold">Evoxia</h1>

      <div className="flex items-center gap-4">
        <button onClick={() => navigate("/notifications")}>
          <Bell />
        </button>

        {user ? (
          <AvatarMenu user={user} />
        ) : (
          <div className="flex gap-2">
            <button onClick={() => navigate("/login")}>Login</button>
            <button onClick={() => navigate("/signup")}>Signup</button>
          </div>
        )}
      </div>
    </div>
  )
}
