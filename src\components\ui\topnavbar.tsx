import { Bell, LogIn, UserCircle } from "lucide-react"
import { Link, useNavigate } from "react-router-dom"
import { useState } from "react"
import logo from '../../../public/no_bg_image.png'

export default function TopNavbar() {
  const navigate = useNavigate()
  const [user] = useState(null) // Replace with your auth logic

  return (
    <nav className="w-full bg-[#18181b] sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-3 md:px-6 lg:px-8">
        <div className="flex justify-between items-center h-14 md:h-16">
          {/* Logo Section */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-2">
              <img src={logo} alt="kadzia logo" className="h-8 w-8 object-contain" />
              <span className="text-[#2de4c2] text-xl font-bold tracking-tight">kadzia</span>
            </Link>
          </div>

          {/* Desktop Navigation Links - Hidden on mobile */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              to="/"
              className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
            >
              Home
            </Link>
            <Link
              to="/messaging"
              className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
            >
              Messaging
            </Link>
            <Link
              to="/explore"
              className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
            >
              Explore
            </Link>
            <Link
              to="/shows"
              className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
            >
              Shows
            </Link>
          </div>

          {/* Right Section - Navigation Items */}
          <div className="flex items-center space-x-2 md:space-x-4">
            {/* Notifications */}
            <button
              onClick={() => navigate("/notifications")}
              className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              aria-label="Notifications"
            >
              <Bell className="h-5 w-5 md:h-6 md:w-6 text-white" />
              <span className="hidden md:inline ml-1 text-sm font-medium">
                Notifications
              </span>
              {/* Notification badge */}
              <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
            </button>

            {/* User Section */}
            {user ? (
              <div className="flex items-center">
                {/* Replace with AvatarMenu component when available */}
                <div className="w-8 h-8 bg-[#2de4c2] rounded-full flex items-center justify-center">
                  <UserCircle className="w-5 h-5 text-white" />
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-1 md:space-x-2">
                <button
                  onClick={() => navigate("/login")}
                  className="flex items-center px-2 md:px-3 py-1.5 md:py-2 text-sm font-medium text-white hover:text-[#2de4c2] hover:bg-gray-800 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#2de4c2] focus:ring-offset-2"
                >
                  <LogIn className="h-4 w-4 md:h-5 md:w-5" />
                  <span className="hidden md:inline ml-1">Login</span>
                </button>
                <button
                  onClick={() => navigate("/signup")}
                  className="flex items-center px-2 md:px-4 py-1.5 md:py-2 text-sm font-medium text-white bg-[#2de4c2] hover:bg-[#1fc9a7] rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#2de4c2] focus:ring-offset-2"
                >
                  <UserCircle className="h-4 w-4 md:h-5 md:w-5" />
                  <span className="hidden md:inline ml-1">Sign Up</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  )
}
