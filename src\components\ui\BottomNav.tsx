// src/components/layout/BottomNavbar.jsx
import { NavLink } from "react-router-dom"
import { Home, MessageCircle, Search, Tv } from "lucide-react"

const links = [
  { to: "/", icon: <Home />, label: "Home" },
  { to: "/messaging", icon: <MessageCircle />, label: "Messages" },
  { to: "/explore", icon: <Search />, label: "Explore" },
  { to: "/shows", icon: <Tv />, label: "Shows" },
]

export default function BottomNavbar() {
  return (
    <nav className="fixed bottom-0 w-full bg-[#18181b] flex justify-around items-center h-16 z-50">
      {links.map(({ to, icon }) => (
        <NavLink
          key={to}
          to={to}
          className={({ isActive }) =>
            `flex flex-col items-center justify-center text-xl transition-colors duration-200 \
            ${isActive ? "text-[#2de4c2]" : "text-gray-400"}`
          }
        >
          {icon}
        </NavLink>
      ))}
    </nav>
  )
}


