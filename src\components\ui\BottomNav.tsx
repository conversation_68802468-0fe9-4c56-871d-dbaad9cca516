// src/components/layout/BottomNavbar.jsx
import { NavLink } from "react-router-dom"
import { Home, MessageCircle, Search, Tv } from "lucide-react"

const links = [
  { to: "/", icon: <Home />, label: "Home" },
  { to: "/messaging", icon: <MessageCircle />, label: "Messages" },
  { to: "/explore", icon: <Search />, label: "Explore" },
  { to: "/shows", icon: <Tv />, label: "Shows" },
]

export default function BottomNavbar() {
  return (
    <nav className="fixed bottom-0 w-full bg-white border-t flex justify-around py-2 z-50">
      {links.map(({ to, icon, label }) => (
        <NavLink
          key={to}
          to={to}
          className={({ isActive }) =>
            `flex flex-col items-center text-sm ${
              isActive ? "text-blue-600" : "text-gray-500"
            }`
          }
        >
          {icon}
          <span>{label}</span>
        </NavLink>
      ))}
    </nav>
  )
}


